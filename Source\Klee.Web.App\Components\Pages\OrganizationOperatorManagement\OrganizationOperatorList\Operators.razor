@layout OrganizationViewLayout
@using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorCreate
@using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorUnavailability
@using AntDesign
@using System.Linq.Expressions
@using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators.Data;
@using Klee.Domain.Entities.OperatorManagement.Operators.Data;
@using Klee.Web.App.Components.UI
@using EnumsNET

@page "/my-assets/operators"

@inherits OperatorsViewBase

<Modal Title="Delete Operator"
       Visible="@this.DeleteModalVisible"
       OnOk="@this.OnConfirmDelete"
       OnCancel="@this.OnCancelDelete"
       OkText="@("Delete")"
       CancelText="@("Cancel")"
       OkButtonProps="@(new ButtonProps { Danger = true })">
    <div>
        <p>Are you sure you want to delete operator '<strong>@CurrentOperatorName</strong>'?</p>
    </div>
</Modal>

<div class="container py-8">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-teal-700">My Operators</h1>

        @if(this.IsUserOrganizationAdmin){

            <Button Type="@ButtonType.Primary"
                    Class="@TailwindStyleStrings.Button.Primary"
                    OnClick="OnClick_AddOperator">
                <i class="fas fa-plus h-4 w-4 mr-2"></i>
                Add Operator
            </Button>
        }

    </div>

    <Card Class="@TailwindStyleStrings.Card.Container">
        <div class="overflow-x-auto">
            <Table TItem="OrganizationOperatorListItem"
                   DataSource="@this.ViewModel.OrganizationOperators"
                   Class="@TailwindStyleStrings.Table.Container"
                   ScrollX="1200"
                   ExpandIconColumnIndex="0"
                   RowExpandable="@(record => true)">
                <ColumnDefinitions>
                    <PropertyColumn Property="@(o => o.OperatorDisplayName)" Title="Name" Sortable Filterable Width="150">
                        <Template>
                            <div class="flex items-center gap-2">
                                <i class="@($"fas fa-user h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                <span class="font-medium">@context.OperatorDisplayName</span>
                            </div>
                        </Template>
                    </PropertyColumn>
                    <PropertyColumn Property="@(o => o.Email)" Title="Email" Sortable Filterable Width="200" />
                    <PropertyColumn Property="@(o => o.HourlyRateInEuros)" Title="Hourly rate (€)" Sortable Filterable Width="120">
                        <Template>
                            € @context.HourlyRateInEuros.ToString("N2")
                        </Template>
                    </PropertyColumn>
                    <PropertyColumn Property="@(o => o.YearsOfExperience)" Title="Years of Experience" Sortable Filterable Width="140">
                        <Template>
                            <div class="text-center">
                                <span class="font-medium">@context.YearsOfExperience</span>
                                <span class="text-xs text-gray-500 ml-1">years</span>
                            </div>
                        </Template>
                    </PropertyColumn>
                    <PropertyColumn Property="@(o => o.YearsOfRemoteExperience)" Title="Remote Experience" Sortable Filterable Width="140">
                        <Template>
                            <div class="text-center">
                                <span class="font-medium">@context.YearsOfRemoteExperience</span>
                                <span class="text-xs text-gray-500 ml-1">years</span>
                            </div>
                        </Template>
                    </PropertyColumn>
                    <PropertyColumn Property="@(o => o.Qualifications)" Title="Qualifications" Width="200">
                        <Template>
                            <div class="flex flex-wrap gap-1">
                                @foreach (var qualification in context.Qualifications.Take(2))
                                {
                                    <span class="@($"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {TailwindStyleStrings.Badge.Info}")">
                                        @qualification.AsString(EnumFormat.DisplayName)
                                    </span>
                                }
                                @if (context.Qualifications.Count > 2)
                                {
                                    <span class="@($"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-teal-100 text-teal-800 border-teal-200")">
                                        <i class="fas fa-chevron-down h-2 w-2 mr-1"></i>
                                        +@(context.Qualifications.Count - 2) more
                                    </span>
                                }
                                @if (context.Qualifications.Count == 0)
                                {
                                    <span class="@($"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {TailwindStyleStrings.Badge.Default}")">
                                        No qualifications
                                    </span>
                                }
                            </div>
                        </Template>
                    </PropertyColumn>
                    <ActionColumn Title="Actions" Fixed="ColumnFixPlacement.Right" Width="160">
                        @if (this.IsUserOrganizationAdmin) {
                            <Space>
                                <SpaceItem>
                                    <Button Type="@ButtonType.Link"
                                            Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                            OnClick="@(() => this.OnClick_EditOperator(context.OperatorId))">
                                        <i class="@($"fas fa-pencil-alt h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <span class="sr-only">Edit</span>
                                    </Button>
                                </SpaceItem>
                                <SpaceItem>
                                    <Button Type="@ButtonType.Link"
                                            Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                            OnClick="@(() => OnClick_ManageUnavailability(context.OperatorId, context.OperatorDisplayName))">
                                        <i class="@($"fas fa-calendar-times h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <span class="sr-only">Manage Unavailability</span>
                                    </Button>
                                </SpaceItem>
                                <SpaceItem>
                                    <Button Type="@ButtonType.Link"
                                            Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                            OnClick="@(() => OnClick_Delete(context.OperatorId, context.OperatorDisplayName))">
                                        <i class="@($"fas fa-trash h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <span class="sr-only">Delete</span>
                                    </Button>
                                </SpaceItem>
                            </Space>
                        }
                    </ActionColumn>
                </ColumnDefinitions>
                <ExpandTemplate Context="record">
                    <div class="p-4">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

                            <!-- Biography Column -->
                            <Card Class="@TailwindStyleStrings.Card.Container">
                                <div class="@TailwindStyleStrings.Card.Header">
                                    <div class="flex items-center gap-2">
                                        <i class="@($"fas fa-user-edit h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <h4 class="font-medium">Biography</h4>
                                    </div>
                                </div>
                                <div class="@TailwindStyleStrings.Card.Content">
                                    @if (!string.IsNullOrWhiteSpace(record.Data.Biography))
                                    {
                                        <p class="text-sm text-gray-700 leading-relaxed">@record.Data.Biography</p>
                                    }
                                    else
                                    {
                                        <div class="text-center py-8">
                                            <i class="fas fa-info-circle h-5 w-5 text-gray-400 mb-2"></i>
                                            <p class="text-sm text-gray-500 font-medium">No biography provided</p>
                                            <p class="text-xs text-gray-400 mt-1">Operator hasn't added a biography yet</p>
                                        </div>
                                    }
                                </div>
                            </Card>

                            <!-- Qualifications Column -->
                            <Card Class="@TailwindStyleStrings.Card.Container">
                                <div class="@TailwindStyleStrings.Card.Header">
                                    <div class="flex items-center gap-2">
                                        <i class="@($"fas fa-certificate h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <h4 class="font-medium">All Qualifications</h4>
                                    </div>
                                </div>
                                <div class="@TailwindStyleStrings.Card.Content">
                                    @if (record.Data.Qualifications.Count > 0)
                                    {
                                        <div class="flex flex-wrap gap-2">
                                            @foreach (var qualification in record.Data.Qualifications)
                                            {
                                                <span class="@($"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {TailwindStyleStrings.Badge.Info}")">
                                                    @qualification.AsString(EnumFormat.DisplayName)
                                                </span>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center py-8">
                                            <i class="fas fa-info-circle h-5 w-5 text-gray-400 mb-2"></i>
                                            <p class="text-sm text-gray-500 font-medium">No qualifications assigned</p>
                                            <p class="text-xs text-gray-400 mt-1">Contact your administrator to assign qualifications</p>
                                        </div>
                                    }
                                </div>
                            </Card>

                            <!-- Working Schedule Column -->
                            <Card Class="@TailwindStyleStrings.Card.Container">
                                <div class="@TailwindStyleStrings.Card.Header">
                                    <div class="flex items-center gap-2">
                                        <i class="@($"fas fa-calendar-alt h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <h4 class="font-medium">Working Schedule</h4>
                                    </div>
                                </div>
                                <div class="@TailwindStyleStrings.Card.Content">
                                    <div class="space-y-4">
                                        <!-- Working Days -->
                                        <div>
                                            <h5 class="text-sm font-medium text-gray-700 mb-3">Working Days</h5>
                                            <div class="space-y-2">
                                                <!-- Weekdays Row -->
                                                <div class="flex flex-wrap gap-3">
                                                    @foreach (var day in GetWorkingDaysDisplay(record.Data.WeekDays).Take(5))
                                                    {
                                                        <div class="flex items-center gap-1">
                                                            <input type="checkbox" checked="@day.IsChecked" disabled class="@(day.IsChecked ? "text-teal-600 focus:ring-teal-500 border-teal-300" : "text-gray-400 border-gray-300") pointer-events-none" />
                                                            <span class="text-xs font-medium text-gray-700">@day.Name</span>
                                                        </div>
                                                    }
                                                </div>
                                                <!-- Weekend Row -->
                                                <div class="flex flex-wrap gap-3">
                                                    @foreach (var day in GetWorkingDaysDisplay(record.Data.WeekDays).Skip(5))
                                                    {
                                                        <div class="flex items-center gap-1">
                                                            <input type="checkbox" checked="@day.IsChecked" disabled class="@(day.IsChecked ? "text-teal-600 focus:ring-teal-500 border-teal-300" : "text-gray-400 border-gray-300") pointer-events-none" />
                                                            <span class="text-xs font-medium text-gray-700">@day.Name</span>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Working Hours -->
                                        <div>
                                            <h5 class="text-sm font-medium text-gray-700 mb-2">Working Hours</h5>
                                            <div class="flex items-center gap-2">
                                                <span class="text-sm text-gray-600">@record.Data.RegularStartTime.ToString("HH:mm")</span>
                                                <span class="text-gray-400">-</span>
                                                <span class="text-sm text-gray-600">@record.Data.RegularEndTime.ToString("HH:mm")</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        </div>
                    </div>
                </ExpandTemplate>
            </Table>
        </div>
    </Card>
</div>

<!-- Manage Unavailability Modal -->
<ManageUnavailability @bind-Visible="ShowUnavailabilityModal"
                      OperatorId="@SelectedOperatorId"
                      OperatorName="@SelectedOperatorName"
                      OnUnavailabilityUpdated="OnUnavailabilityUpdated" />

@code {

    #region METHODS - STATIC
    public static string GetUri()
    {
        return "/my-assets/operators";
    }
    #endregion

    #region METHODS - HELPER
    private List<(string Name, bool IsChecked)> GetWorkingDaysDisplay(WeekDaysIds weekDays)
    {
        return new List<(string Name, bool IsChecked)>
        {
            ("Mon", weekDays.HasFlag(WeekDaysIds.Monday)),
            ("Tue", weekDays.HasFlag(WeekDaysIds.Tuesday)),
            ("Wed", weekDays.HasFlag(WeekDaysIds.Wednesday)),
            ("Thu", weekDays.HasFlag(WeekDaysIds.Thursday)),
            ("Fri", weekDays.HasFlag(WeekDaysIds.Friday)),
            ("Sat", weekDays.HasFlag(WeekDaysIds.Saturday)),
            ("Sun", weekDays.HasFlag(WeekDaysIds.Sunday))
        };
    }
    #endregion

}
