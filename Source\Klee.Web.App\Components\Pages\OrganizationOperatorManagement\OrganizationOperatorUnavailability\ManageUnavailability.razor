@using AntDesign
@using Klee.Domain.Entities.OperatorManagement.Operators.Data
@using Klee.Web.App.Components.UI
@using EnumsNET

@inherits ManageUnavailabilityViewBase

<Modal Title="@($"Manage Unavailability - {OperatorName}")"
       @bind-Visible="Visible"
       OnOk="HandleModalOk"
       OnCancel="HandleModalCancel"
       Width="800"
       OkText=@("Close")
       CancelText=@("Cancel")>
    
    @if (IsLoading)
    {
        <div class="flex justify-center items-center py-8">
            <Spin Size=SpinSize.Large />
        </div>
    }
    else
    {
        <div class="space-y-6">
            <!-- Add New Unavailability Period -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-medium text-teal-700 mb-4">Add Unavailability Period</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-teal-700 mb-2">Date Range</label>
                        <RangePicker @bind-Value="SelectedDateRange"
                                   OnChange="HandleDateRangeChange"
                                   Class="@TailwindStyleStrings.Form.Input"
                                   Placeholder="@(new[] { "Start Date", "End Date" })" />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-teal-700 mb-2">Reason</label>
                        <Select TItemValue="string" TItem="string"
                                @bind-Value="ViewModel.SelectedReasonDisplayName"
                                Class="@TailwindStyleStrings.Form.Select"
                                Placeholder="Select reason">
                            @foreach (var reason in ManageUnavailabilityViewModel.SelectableUnavailabilityReasonDisplayNames)
                            {
                                <SelectOption TItemValue="string" TItem="string" Value="@reason">@reason</SelectOption>
                            }
                        </Select>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-teal-700 mb-2">Notes (Optional)</label>
                    <TextArea @bind-Value="ViewModel.Notes"
                              Class="@TailwindStyleStrings.Form.TextArea"
                              Placeholder="Additional notes about this unavailability period"
                              Rows="2"
                              MaxLength="500" />
                </div>
                
                <Button Type="@ButtonType.Primary"
                        Class="@TailwindStyleStrings.Button.Primary"
                        OnClick="HandleAddPeriod"
                        Disabled="@(!ViewModel.CanAddPeriod)">
                    <i class="fas fa-plus h-4 w-4 mr-2"></i>
                    Add Period
                </Button>
            </div>

            <!-- Current Unavailability Periods -->
            <div>
                <h3 class="text-lg font-medium text-teal-700 mb-4">Current Unavailability Periods</h3>
                
                @if (ViewModel.UnavailabilityPeriods.Any())
                {
                    <div class="space-y-3">
                        @foreach (var period in ViewModel.UnavailabilityPeriods.OrderBy(p => p.StartDate))
                        {
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-2">
                                            <span class="@($"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border {GetReasonBadgeClass(period.Reason)}")">
                                                @GetReasonDisplayName(period.Reason)
                                            </span>
                                            <span class="text-sm font-medium text-gray-900">
                                                @period.StartDate.ToString("MMM dd, yyyy") - @period.EndDate.ToString("MMM dd, yyyy")
                                            </span>
                                        </div>
                                        
                                        @if (!string.IsNullOrWhiteSpace(period.Notes))
                                        {
                                            <p class="text-sm text-gray-600">@period.Notes</p>
                                        }
                                    </div>
                                    
                                    <Button Type="@ButtonType.Link"
                                            Class="@($"{TailwindStyleStrings.Button.DangerGhost} h-8 w-8 p-0")"
                                            OnClick="@(() => HandleRemovePeriod(period))">
                                        <i class="fas fa-trash h-4 w-4"></i>
                                        <span class="sr-only">Remove</span>
                                    </Button>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-calendar-check text-3xl mb-2"></i>
                        <p>No unavailability periods defined</p>
                    </div>
                }
            </div>

            <!-- Calendar View -->
            <div>
                <h3 class="text-lg font-medium text-teal-700 mb-4">Calendar Overview</h3>
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <p class="text-sm text-gray-600 mb-2">Unavailable dates are highlighted in the periods above.</p>
                    <div class="flex items-center gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-red-100 border border-red-200 rounded"></div>
                            <span class="text-gray-600">Unavailable periods</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</Modal>
