@using Klee.Domain.Entities.OperatorManagement.Operators.Data
@using Monet.Helpers

@inherits ManageUnavailabilityViewBase


@if (IsLoading)
{
    <div class="flex justify-center items-center py-8">
        <Spin Size="@SpinSize.Large" />
    </div>
}
else
{
    <div class="space-y-6">
        <!-- Add New Unavailable Date -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-teal-700 mb-4">Add unavailability period</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-teal-700 mb-2">Date</label>
                    <RangePicker 
                            TValue="DateTime?[]"
                                 OnChange="OnDateRangeChanged"
                                 Class="@TailwindStyleStrings.Form.Input"/>
                </div>

                <div>
                    <label class="block text-sm font-medium text-teal-700 mb-2">Reason</label>
                    <EnumSelect TEnum="UnavailabilityReasonIds"
                                @bind-Value="@SelectedReason"
                                Placeholder="Select reaason for unavailability"
                                Class="@TailwindStyleStrings.Form.Select"
                                AllowClear="false"
                                Id="unavailabilityType">
                    </EnumSelect>
                </div>
            </div>

            <Button Type="@ButtonType.Primary"
                    Class="@TailwindStyleStrings.Button.Primary"
                    OnClick="OnClick_AddDate">
                <i class="fas fa-plus h-4 w-4 mr-2"></i>
                Add Date
            </Button>
        </div>

        <!-- Current Unavailability Periods -->
        @if (UnavailableDates.Any())
        {
            <div class="bg-white p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-teal-700 mb-4">Current Unavailability Periods</h3>
                <div class="space-y-2 max-h-48 overflow-y-auto">
                    @foreach (var period in UnavailableDates.OrderBy(d => d.StartDate))
                    {
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex items-center gap-3">
                                <span class="@($"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border {GetBadgeClassForReason(period.Reason)}")">
                                    @period.Reason.GetDisplayName()
                                </span>
                                <span class="text-sm font-medium text-gray-900">
                                    @if (period.StartDate.Date == period.EndDate.Date)
                                    {
                                        @period.StartDate.ToString("MMM dd, yyyy")
                                    }
                                    else
                                    {
                                        @($"{period.StartDate:MMM dd} - {period.EndDate:MMM dd, yyyy}")
                                    }
                                </span>
                            </div>

                            <Button Type="@ButtonType.Link"
                                    Class="@($"{TailwindStyleStrings.Button.DangerGhost} h-8 w-8 p-0")"
                                    OnClick="@(() => HandleRemovePeriod(period))">
                                <i class="fas fa-trash h-4 w-4"></i>
                                <span class="sr-only">Remove</span>
                            </Button>
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Calendar View -->
        <div class="bg-white p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-teal-700 mb-4">Unavailability Calendar</h3>
            <div class="calendar-container" style="max-height: 400px; overflow-y: auto;">
                <Calendar DateCellRender="CalenderDateCellRender" />
            </div>
        </div>

    </div>
}

@code
{
    class OperatorUnavailabilityData
    {
        public BadgeStatus Status { get; set; }
        public string Content { get; set; } = "";
        public UnavailabilityReasonIds Reason { get; set; }
    }

    private List<OperatorUnavailabilityData> GetUnavailabilityData(DateTime date)
    {
        List<OperatorUnavailabilityData> unavailabilityData = new();

        // Check all unavailability periods for this date
        foreach (OperatorUnavailabilityPeriod period in UnavailableDates)
        {
            // Check if the date falls within this unavailability period
            if (date.Date >= period.StartDate.Date && date.Date <= period.EndDate.Date)
            {
                BadgeStatus status = GetBadgeStatusForReason(period.Reason);
                string? content = period.Reason.GetDisplayName();

                unavailabilityData.Add(new OperatorUnavailabilityData
                {
                    Status = status,
                    Content = content,
                    Reason = period.Reason
                });
            }
        }

        return unavailabilityData;
    }

    private BadgeStatus GetBadgeStatusForReason(UnavailabilityReasonIds reason)
    {
        return reason switch
        {
            UnavailabilityReasonIds.SickLeave => BadgeStatus.Error,     // Red
            UnavailabilityReasonIds.Holiday => BadgeStatus.Processing, // Blue
            UnavailabilityReasonIds.Training => BadgeStatus.Success,   // Green
            UnavailabilityReasonIds.Other => BadgeStatus.Default,      // Gray
            _ => BadgeStatus.Default
        };
    }

    private RenderFragment CalenderDateCellRender(DateTime value) {
        value = DateTime.SpecifyKind(value, DateTimeKind.Local);
        List<OperatorUnavailabilityData> unavailabilityData = GetUnavailabilityData(value);
        string tooltipContent = GetTooltipContent(value, unavailabilityData);

        return @<Template>
                    <Tooltip Title="@tooltipContent">
                        <ul class="unavailability-events">
                            @foreach (OperatorUnavailabilityData data in unavailabilityData)
                            {
                                <li key="@data.Content">
                                    <Badge Status="@data.Status" Text="@data.Content" />
                                </li>
                            }
                        </ul>
                    </Tooltip>
                </Template>;
    }

    private string GetTooltipContent(DateTime date, List<OperatorUnavailabilityData> unavailabilityData)
    {
        var content = $"Unavailable on {date:MMM dd, yyyy}:\n";

        foreach (var data in unavailabilityData)
        {
            // Find the specific period for this data to get date range
            var period = UnavailableDates.FirstOrDefault(p =>
                date.Date >= p.StartDate.Date &&
                date.Date <= p.EndDate.Date &&
                p.Reason == data.Reason);

            if (period != null)
            {
                if (period.StartDate.Date == period.EndDate.Date)
                {
                    content += $"• {data.Content} (Single day)\n";
                }
                else
                {
                    content += $"• {data.Content} ({period.StartDate:MMM dd} - {period.EndDate:MMM dd})\n";
                }
            }
            else
            {
                content += $"• {data.Content}\n";
            }
        }

        return content.TrimEnd('\n');
    }

    private void HandleRemovePeriod(OperatorUnavailabilityPeriod period)
    {
        UnavailableDates.Remove(period);
        StateHasChanged();
    }

    private string GetBadgeClassForReason(UnavailabilityReasonIds reason)
    {
        return reason switch
        {
            UnavailabilityReasonIds.SickLeave => "bg-red-100 text-red-800 border-red-200",
            UnavailabilityReasonIds.Holiday => "bg-blue-100 text-blue-800 border-blue-200",
            UnavailabilityReasonIds.Training => "bg-green-100 text-green-800 border-green-200",
            UnavailabilityReasonIds.Other => "bg-gray-100 text-gray-800 border-gray-200",
            _ => "bg-gray-100 text-gray-800 border-gray-200"
        };
    }
}

<Style>
    .unavailability-events {
        list-style: none;
        margin: 0;
        padding: 0;
    }

        .unavailability-events .ant-badge-status {
            overflow: hidden;
            white-space: nowrap;
            width: 100%;
            text-overflow: ellipsis;
            font-size: 12px;
        }

    .calendar-container {
        border-radius: 6px;
        border: 1px solid #e5e7eb;
    }

    /* Responsive calendar adjustments */
    @@media (max-width: 768px) {
        .calendar-container {
            max-height: 300px;
        }
    }

    /* Ensure calendar fits well in container */
    .calendar-container .ant-picker-calendar {
        background: transparent;
        border: none;
    }

    .calendar-container .ant-picker-calendar-header {
        padding: 8px 12px;
        border-bottom: 1px solid #e5e7eb;
    }
</Style>
