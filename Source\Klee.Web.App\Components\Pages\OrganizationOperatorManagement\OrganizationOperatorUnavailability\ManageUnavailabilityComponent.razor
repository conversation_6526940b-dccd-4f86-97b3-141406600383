@using Klee.Domain.Entities.OperatorManagement.Operators.Data
@using Monet.Helpers

@inherits ManageUnavailabilityViewBase


@if (IsLoading)
{
    <div class="flex justify-center items-center py-8">
        <Spin Size="@SpinSize.Large" />
    </div>
}
else
{
    <div class="space-y-6">
        <!-- Add New Unavailable Date -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-teal-700 mb-4">Add unavailability period</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-teal-700 mb-2">Date</label>
                    <RangePicker 
                            TValue="DateTime?[]"
                                 OnChange="OnDateRangeChanged"
                                 Class="@TailwindStyleStrings.Form.Input"/>
                </div>

                <div>
                    <label class="block text-sm font-medium text-teal-700 mb-2">Reason</label>
                    <EnumSelect TEnum="UnavailabilityReasonIds"
                                @bind-Value="@SelectedReason"
                                Placeholder="Select reaason for unavailability"
                                Class="@TailwindStyleStrings.Form.Select"
                                AllowClear="false"
                                Id="unavailabilityType">
                    </EnumSelect>
                </div>
            </div>

            <Button Type="@ButtonType.Primary"
                    Class="@TailwindStyleStrings.Button.Primary"
                    OnClick="OnClick_AddDate">
                <i class="fas fa-plus h-4 w-4 mr-2"></i>
                Add Date
            </Button>
        </div>

        <!-- Calendar View -->
        <div class="bg-white p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-teal-700 mb-4">Unavailability Calendar</h3>
            <Calendar DateCellRender="CalenderDateCellRender" />
        </div>

    </div>
}

@code
{
    class OperatorUnavailabilityData
    {
        public BadgeStatus Status { get; set; }
        public string Content { get; set; } = "";
        public UnavailabilityReasonIds Reason { get; set; }
    }

    private List<OperatorUnavailabilityData> GetUnavailabilityData(DateTime date)
    {
        List<OperatorUnavailabilityData> unavailabilityData = new();

        // Check all unavailability periods for this date
        foreach (OperatorUnavailabilityPeriod period in UnavailableDates)
        {
            // Check if the date falls within this unavailability period
            if (date.Date >= period.StartDate.Date && date.Date <= period.EndDate.Date)
            {
                BadgeStatus status = GetBadgeStatusForReason(period.Reason);
                string? content = period.Reason.GetDisplayName();

                unavailabilityData.Add(new OperatorUnavailabilityData
                {
                    Status = status,
                    Content = content,
                    Reason = period.Reason
                });
            }
        }

        return unavailabilityData;
    }

    private BadgeStatus GetBadgeStatusForReason(UnavailabilityReasonIds reason)
    {
        return reason switch
        {
            UnavailabilityReasonIds.SickLeave => BadgeStatus.Error,     // Red
            UnavailabilityReasonIds.Holiday => BadgeStatus.Processing, // Blue
            UnavailabilityReasonIds.Training => BadgeStatus.Success,   // Green
            UnavailabilityReasonIds.Other => BadgeStatus.Default,      // Gray
            _ => BadgeStatus.Default
        };
    }

    private RenderFragment CalenderDateCellRender(DateTime value) {
        value = DateTime.SpecifyKind(value, DateTimeKind.Local);
        List<OperatorUnavailabilityData> unavailabilityData = GetUnavailabilityData(value);
        string tooltipContent = GetTooltipContent(value, unavailabilityData);

        return @<Template>
                    <Tooltip Title="@tooltipContent">
                        <ul class="unavailability-events">
                            @foreach (OperatorUnavailabilityData data in unavailabilityData)
                            {
                                <li key="@data.Content">
                                    <Badge Status="@data.Status" Text="@data.Content" />
                                </li>
                            }
                        </ul>
                    </Tooltip>
                </Template>;
    }

    private string GetTooltipContent(DateTime date, List<OperatorUnavailabilityData> unavailabilityData)
    {
        var content = $"Unavailable on {date:MMM dd, yyyy}:\n";

        foreach (var data in unavailabilityData)
        {
            // Find the specific period for this data to get date range
            var period = UnavailableDates.FirstOrDefault(p =>
                date.Date >= p.StartDate.Date &&
                date.Date <= p.EndDate.Date &&
                p.Reason == data.Reason);

            if (period != null)
            {
                if (period.StartDate.Date == period.EndDate.Date)
                {
                    content += $"• {data.Content} (Single day)\n";
                }
                else
                {
                    content += $"• {data.Content} ({period.StartDate:MMM dd} - {period.EndDate:MMM dd})\n";
                }
            }
            else
            {
                content += $"• {data.Content}\n";
            }
        }

        return content.TrimEnd('\n');
    }
}

<Style>
    .unavailability-events {
        list-style: none;
        margin: 0;
        padding: 0;
    }

        .unavailability-events .ant-badge-status {
            overflow: hidden;
            white-space: nowrap;
            width: 100%;
            text-overflow: ellipsis;
            font-size: 12px;
        }
</Style>
