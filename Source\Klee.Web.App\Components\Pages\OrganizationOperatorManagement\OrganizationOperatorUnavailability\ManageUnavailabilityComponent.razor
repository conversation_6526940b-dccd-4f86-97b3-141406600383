@using Klee.Domain.Entities.OperatorManagement.Operators.Data
@using Monet.Helpers

@inherits ManageUnavailabilityViewBase


@if (IsLoading)
{
    <div class="flex justify-center items-center py-8">
        <Spin Size="@SpinSize.Large" />
    </div>
}
else
{
    <div class="space-y-6">
        <!-- Add New Unavailable Date -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-teal-700 mb-4">Add unavailability period</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-teal-700 mb-2">Date</label>
                    <RangePicker 
                            TValue="DateTime?[]"
                                 OnChange="OnDateRangeChanged"
                                 Class="@TailwindStyleStrings.Form.Input"/>
                </div>

                <div>
                    <label class="block text-sm font-medium text-teal-700 mb-2">Reason</label>
                    <EnumSelect TEnum="UnavailabilityReasonIds"
                                @bind-Value="@SelectedReason"
                                Placeholder="Select reaason for unavailability"
                                Class="@TailwindStyleStrings.Form.Select"
                                AllowClear="false"
                                Id="unavailabilityType">
                    </EnumSelect>
                </div>
            </div>

            <Button Type="@ButtonType.Primary"
                    Class="@TailwindStyleStrings.Button.Primary"
                    OnClick="OnClick_AddDate">
                <i class="fas fa-plus h-4 w-4 mr-2"></i>
                Add Date
            </Button>
        </div>

    </div>
}
