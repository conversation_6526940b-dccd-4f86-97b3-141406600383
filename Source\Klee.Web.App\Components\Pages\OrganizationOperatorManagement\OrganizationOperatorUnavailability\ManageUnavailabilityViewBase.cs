using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Services;
using Microsoft.AspNetCore.Components;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorUnavailability;

public class ManageUnavailabilityViewBase : ComponentBase
{
    #region SERVICES

    [Inject] protected ISrpProcessors SrpProcessors { get; set; }
    [Inject] protected INotificationService NotificationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter] public string OperatorId { get; set; } = "";
    #endregion

    #region PROPERTIES
    protected bool IsLoading { get; set; } = true;
    protected string OperatorDisplayName { get; set; } = "";

    public List<DateTime> UnavailableDateRange { get; set; } = new ();
    protected UnavailabilityReasonIds SelectedReason { get; set; } = UnavailabilityReasonIds.SickLeave;
    public List<OperatorUnavailabilityPeriod> UnavailableDates { get; set; } = new();
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(OperatorId))
        {
            await LoadDataAsync();
        }
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - DATA
    public async Task LoadDataAsync()
    {
        try
        {
            IsLoading = true;
            StateHasChanged();

            IQueryContext queryContext = await SrpProcessors.GetQueryContextAsync();
            GetOrganizationOperatorQuery query = new (OperatorId, queryContext);
            Operator? result = await SrpProcessors.QueryProcessor.ExecuteAsync(query);

            if (result != null)
            {
                this.OperatorDisplayName = result.DisplayName;
                UnavailableDates = result.UnavailabilityPeriods.ToList();
            }
        }
        catch (Exception)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to load operator data. Please try again.",
                Duration = 4.5
            });
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }
    #endregion

    #region METHODS - EVENT HANDLERS
    protected void OnClick_AddDate()
    {
        if (UnavailableDateRange.Count == 2)
        {
            var newPeriod = new OperatorUnavailabilityPeriod
            {
                StartDate = DateTime.SpecifyKind(UnavailableDateRange[0].Date, DateTimeKind.Local),
                EndDate = DateTime.SpecifyKind(UnavailableDateRange[1].Date, DateTimeKind.Local),
                Reason = SelectedReason,
            };

            //If start date already exists, update it
            foreach (OperatorUnavailabilityPeriod operatorUnavailabilityPeriod in UnavailableDates) {
                if(operatorUnavailabilityPeriod.StartDate.Date == newPeriod.StartDate.Date)
                {
                    operatorUnavailabilityPeriod.Reason = newPeriod.Reason;
                    operatorUnavailabilityPeriod.EndDate = newPeriod.EndDate;
                    break;
                }
                //If start date does not exist, add it
                else
                {
                    UnavailableDates.Add(newPeriod);
                    break;
                }
            }
            StateHasChanged();
   
        }
    }

    protected void OnDateRangeChanged(DateRangeChangedEventArgs<DateTime?[]> args)
    {
        if (args.Dates.Length == 2) {
            UnavailableDateRange = args.Dates.Select(d => d.Value).ToList();
        }
    }
    #endregion
}
