using System;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Services;
using Microsoft.AspNetCore.Components;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorUnavailability;

public class ManageUnavailabilityViewBase : ComponentBase
{
    #region SERVICES
    [Inject] protected ISrpProcessors SrpProcessors { get; set; } = default!;
    [Inject] protected INotificationService NotificationService { get; set; } = default!;
    #endregion

    #region PARAMETERS
    [Parameter] public string OperatorId { get; set; } = "";
    [Parameter] public string OperatorName { get; set; } = "";
    [Parameter] public bool Visible { get; set; }
    [Parameter] public EventCallback<bool> VisibleChanged { get; set; }
    [Parameter] public EventCallback OnUnavailabilityUpdated { get; set; }
    #endregion

    #region PROPERTIES
    protected ManageUnavailabilityViewModel ViewModel { get; set; } = default!;
    protected bool IsLoading { get; set; } = true;
    protected DateTime[] SelectedDateRange { get; set; } = Array.Empty<DateTime>();
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        ViewModel = new ManageUnavailabilityViewModel(SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Visible && !string.IsNullOrEmpty(OperatorId))
        {
            await LoadDataAsync();
        }
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - DATA
    protected async Task LoadDataAsync()
    {
        try
        {
            IsLoading = true;
            StateHasChanged();

            await ViewModel.LoadOperatorUnavailabilityAsync(OperatorId, OperatorName);
        }
        catch (Exception ex)
        {
            await NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to load operator unavailability data. Please try again.",
                Duration = 4.5
            });
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }
    #endregion

    #region METHODS - EVENT HANDLERS
    protected async Task HandleModalCancel()
    {
        await CloseModal();
    }

    protected async Task HandleModalOk()
    {
        await CloseModal();
    }

    protected async Task CloseModal()
    {
        await VisibleChanged.InvokeAsync(false);
    }

    protected async Task HandleAddPeriod()
    {
        try
        {
            await ViewModel.AddUnavailabilityPeriodAsync();
            
            await NotificationService.Success(new NotificationConfig()
            {
                Message = "Success",
                Description = "Unavailability period added successfully.",
                Duration = 4.5
            });

            await OnUnavailabilityUpdated.InvokeAsync();
        }
        catch (InvalidOperationException ex)
        {
            await NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = ex.Message,
                Duration = 4.5
            });
        }
        catch (Exception)
        {
            await NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to add unavailability period. Please try again.",
                Duration = 4.5
            });
        }
    }

    protected async Task HandleRemovePeriod(OperatorUnavailabilityPeriod period)
    {
        try
        {
            await ViewModel.RemoveUnavailabilityPeriodAsync(period);
            
            await NotificationService.Success(new NotificationConfig()
            {
                Message = "Success",
                Description = "Unavailability period removed successfully.",
                Duration = 4.5
            });

            await OnUnavailabilityUpdated.InvokeAsync();
        }
        catch (Exception)
        {
            await NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to remove unavailability period. Please try again.",
                Duration = 4.5
            });
        }
    }

    protected void HandleDateRangeChange(DateRangeChangedEventArgs<DateTime[]> args)
    {
        if (args.Dates != null && args.Dates.Length == 2)
        {
            ViewModel.SelectedStartDate = args.Dates[0];
            ViewModel.SelectedEndDate = args.Dates[1];
        }
        else
        {
            ViewModel.SelectedStartDate = null;
            ViewModel.SelectedEndDate = null;
        }
        StateHasChanged();
    }



    protected string GetReasonDisplayName(UnavailabilityReasonIds reason)
    {
        return reason.ToString() switch
        {
            nameof(UnavailabilityReasonIds.SickLeave) => "Sick Leave",
            nameof(UnavailabilityReasonIds.Holiday) => "Holiday",
            nameof(UnavailabilityReasonIds.Training) => "Training",
            nameof(UnavailabilityReasonIds.Other) => "Other",
            _ => reason.ToString()
        };
    }

    protected string GetReasonBadgeClass(UnavailabilityReasonIds reason)
    {
        return reason switch
        {
            UnavailabilityReasonIds.SickLeave => "bg-red-100 text-red-800 border-red-200",
            UnavailabilityReasonIds.Holiday => "bg-blue-100 text-blue-800 border-blue-200",
            UnavailabilityReasonIds.Training => "bg-green-100 text-green-800 border-green-200",
            UnavailabilityReasonIds.Other => "bg-gray-100 text-gray-800 border-gray-200",
            _ => "bg-gray-100 text-gray-800 border-gray-200"
        };
    }
    #endregion
}
