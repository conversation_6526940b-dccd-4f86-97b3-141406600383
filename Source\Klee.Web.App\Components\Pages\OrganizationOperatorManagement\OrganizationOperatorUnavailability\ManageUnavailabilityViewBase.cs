using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Services;
using Microsoft.AspNetCore.Components;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorUnavailability;

public class ManageUnavailabilityViewBase : ComponentBase
{
    #region SERVICES

    [Inject] protected ISrpProcessors SrpProcessors { get; set; }
    [Inject] protected INotificationService NotificationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter] public string OperatorId { get; set; } = "";
    #endregion

    #region PROPERTIES
    protected bool IsLoading { get; set; } = true;
    protected string OperatorDisplayName { get; set; } = "";

    public List<DateTime> UnavailableDateRange { get; set; } = new ();
    protected UnavailabilityReasonIds SelectedReason { get; set; } = UnavailabilityReasonIds.SickLeave;
    public List<OperatorUnavailabilityPeriod> UnavailableDates { get; set; } = new();
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(OperatorId))
        {
            await LoadDataAsync();
        }
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - DATA
    protected async Task LoadDataAsync()
    {
        try
        {
            IsLoading = true;
            StateHasChanged();

            IQueryContext queryContext = await SrpProcessors.GetQueryContextAsync();
            GetOrganizationOperatorQuery query = new (OperatorId, queryContext);
            Operator? result = await SrpProcessors.QueryProcessor.ExecuteAsync(query);

            if (result != null)
            {
                this.OperatorDisplayName = result.DisplayName;
                UnavailableDates = result.UnavailabilityPeriods.ToList();
            }
        }
        catch (Exception)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to load operator data. Please try again.",
                Duration = 4.5
            });
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }
    #endregion

    #region METHODS - EVENT HANDLERS
    protected void OnClick_AddDate()
    {
        if (UnavailableDateRange.Count == 2)
        {
            var newPeriod = new OperatorUnavailabilityPeriod
            {
                StartDate = UnavailableDateRange[0].Date,
                EndDate = UnavailableDateRange[1].Date,
                Reason = SelectedReason,
            };

            // Check if date already exists
            if (UnavailableDates.All(d => d.StartDate.Date != newPeriod.StartDate.Date))
            {
                UnavailableDates.Add(newPeriod);
                UnavailableDates = UnavailableDates.OrderBy(d => d.StartDate).ToList();
                UnavailableDateRange = null;
                StateHasChanged();
            }
        }
    }

    protected void OnDateRangeChanged(DateRangeChangedEventArgs<DateTime?[]> args)
    {
        if (args.Dates.Length == 2) {
            UnavailableDateRange = args.Dates.Select(d => d.Value).ToList();
        }
    }
    #endregion
}
