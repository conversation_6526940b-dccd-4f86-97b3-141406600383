using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using EnumsNET;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Services;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Messages.Queries.Common;
using Renoir.Srp.Portal.Web.Pages.Common;
using ReactiveUI;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorUnavailability;

public class ManageUnavailabilityViewModel : ReactiveObject
{
    #region FIELDS
    private readonly ISrpProcessors _srpProcessors;
    private string _operatorId = "";
    private string _operatorName = "";
    private List<OperatorUnavailabilityPeriod> _unavailabilityPeriods = new();
    private DateTime? _selectedStartDate;
    private DateTime? _selectedEndDate;
    private UnavailabilityReasonIds _selectedReason = UnavailabilityReasonIds.None;
    private string _notes = "";
    #endregion

    #region PROPERTIES - STATIC
    public static IList<string> SelectableUnavailabilityReasonDisplayNames { get; } = 
        Enums.GetMembers<UnavailabilityReasonIds>()
            .Where(_ => _.Value != UnavailabilityReasonIds.None)
            .Select(_ => _.AsString(EnumFormat.DisplayName))
            .OrderBy(_ => _).ToList();
    #endregion

    #region PROPERTIES
    public string OperatorId
    {
        get => _operatorId;
        set => this.RaiseAndSetIfChanged(ref _operatorId, value);
    }

    public string OperatorName
    {
        get => _operatorName;
        set => this.RaiseAndSetIfChanged(ref _operatorName, value);
    }

    public List<OperatorUnavailabilityPeriod> UnavailabilityPeriods
    {
        get => _unavailabilityPeriods;
        set => this.RaiseAndSetIfChanged(ref _unavailabilityPeriods, value);
    }

    [Required(ErrorMessage = "Start date is required")]
    public DateTime? SelectedStartDate
    {
        get => _selectedStartDate;
        set => this.RaiseAndSetIfChanged(ref _selectedStartDate, value);
    }

    [Required(ErrorMessage = "End date is required")]
    public DateTime? SelectedEndDate
    {
        get => _selectedEndDate;
        set => this.RaiseAndSetIfChanged(ref _selectedEndDate, value);
    }

    [Required(ErrorMessage = "Reason is required")]
    public UnavailabilityReasonIds SelectedReason
    {
        get => _selectedReason;
        set => this.RaiseAndSetIfChanged(ref _selectedReason, value);
    }

    public string SelectedReasonDisplayName
    {
        get => SelectedReason.AsString(EnumFormat.DisplayName);
        set
        {
            if (Enums.TryParse<UnavailabilityReasonIds>(value, EnumFormat.DisplayName, out UnavailabilityReasonIds reason))
            {
                SelectedReason = reason;
            }
        }
    }

    public string Notes
    {
        get => _notes;
        set => this.RaiseAndSetIfChanged(ref _notes, value);
    }

    public bool CanAddPeriod => SelectedStartDate.HasValue && SelectedEndDate.HasValue && 
                               SelectedReason != UnavailabilityReasonIds.None &&
                               SelectedStartDate <= SelectedEndDate;
    #endregion

    #region CONSTRUCTORS
    public ManageUnavailabilityViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS
    public async Task LoadOperatorUnavailabilityAsync(string operatorId, string operatorName)
    {
        OperatorId = operatorId;
        OperatorName = operatorName;

        try
        {
            IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
            GetOrganizationOperatorQuery query = new GetOrganizationOperatorQuery(operatorId, queryContext);

            var result = await _srpProcessors.ExecuteQueryAsync(query);
            if (result != null)
            {
                UnavailabilityPeriods = result.UnavailabilityPeriods?.ToList() ?? new List<OperatorUnavailabilityPeriod>();
            }
        }
        catch (Exception ex)
        {
            // Handle error - could add logging here
            UnavailabilityPeriods = new List<OperatorUnavailabilityPeriod>();
        }
    }

    public async Task AddUnavailabilityPeriodAsync()
    {
        if (!CanAddPeriod) return;

        var newPeriod = new OperatorUnavailabilityPeriod
        {
            StartDate = SelectedStartDate!.Value.Date,
            EndDate = SelectedEndDate!.Value.Date,
            Reason = SelectedReason,
            Notes = Notes ?? ""
        };

        // Check for overlapping periods
        if (HasOverlappingPeriod(newPeriod))
        {
            throw new InvalidOperationException("The selected dates overlap with an existing unavailability period.");
        }

        // Add to local collection
        var updatedPeriods = UnavailabilityPeriods.ToList();
        updatedPeriods.Add(newPeriod);
        UnavailabilityPeriods = updatedPeriods.OrderBy(p => p.StartDate).ToList();

        // Save to database
        await SaveUnavailabilityPeriodsAsync();

        // Clear form
        ClearForm();
    }

    public async Task RemoveUnavailabilityPeriodAsync(OperatorUnavailabilityPeriod period)
    {
        var updatedPeriods = UnavailabilityPeriods.ToList();
        updatedPeriods.Remove(period);
        UnavailabilityPeriods = updatedPeriods;

        await SaveUnavailabilityPeriodsAsync();
    }

    private async Task SaveUnavailabilityPeriodsAsync()
    {
        try
        {
            ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
            UpdateOrganizationOperatorUnavailabilityCommand command = new UpdateOrganizationOperatorUnavailabilityCommand(commandContext)
            {
                OperatorId = OperatorId,
                UnavailabilityPeriods = UnavailabilityPeriods
            };

            await _srpProcessors.ExecuteCommandAsync(command);
        }
        catch (Exception ex)
        {
            // Re-throw to be handled by the UI
            throw new InvalidOperationException("Failed to save unavailability periods. Please try again.", ex);
        }
    }

    private bool HasOverlappingPeriod(OperatorUnavailabilityPeriod newPeriod)
    {
        return UnavailabilityPeriods.Any(existing =>
            (newPeriod.StartDate <= existing.EndDate && newPeriod.EndDate >= existing.StartDate));
    }

    private void ClearForm()
    {
        SelectedStartDate = null;
        SelectedEndDate = null;
        SelectedReason = UnavailabilityReasonIds.None;
        Notes = "";
    }

    public List<DateTime> GetUnavailableDates()
    {
        var unavailableDates = new List<DateTime>();
        
        foreach (var period in UnavailabilityPeriods)
        {
            var currentDate = period.StartDate.Date;
            while (currentDate <= period.EndDate.Date)
            {
                unavailableDates.Add(currentDate);
                currentDate = currentDate.AddDays(1);
            }
        }
        
        return unavailableDates;
    }
    #endregion
}
